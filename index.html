<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Clock Interface</title>
  <style>
    :root {
      /* Light theme colors */
      --bg-light: #ffffff;
      --text-light: #1d1d1f;
      --accent-light: #007aff;
      --glass-light: rgba(255, 255, 255, 0.25);
      --shadow-light: rgba(0, 0, 0, 0.1);
      --neon-light: #00d4ff;

      /* Dark theme colors */
      --bg-dark: #000000;
      --text-dark: #f5f5f7;
      --accent-dark: #0a84ff;
      --glass-dark: rgba(255, 255, 255, 0.1);
      --shadow-dark: rgba(0, 0, 0, 0.3);
      --neon-dark: #00ffff;

      /* Current theme (default to light) */
      --bg-color: var(--bg-light);
      --text-color: var(--text-light);
      --accent-color: var(--accent-light);
      --glass-color: var(--glass-light);
      --shadow-color: var(--shadow-light);
      --neon-color: var(--neon-light);
    }

    [data-theme="dark"] {
      --bg-color: var(--bg-dark);
      --text-color: var(--text-dark);
      --accent-color: var(--accent-dark);
      --glass-color: var(--glass-dark);
      --shadow-color: var(--shadow-dark);
      --neon-color: var(--neon-dark);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
      background: var(--bg-color);
      color: var(--text-color);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s ease, color 0.3s ease;
      overflow-x: hidden;
    }

    .controls {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .control-btn {
      background: var(--glass-color);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 10px 15px;
      color: var(--text-color);
      cursor: pointer;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 15px var(--shadow-color);
    }

    .control-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px var(--shadow-color);
    }

    .main-container {
      text-align: center;
      padding: 0;
      width: 100%;
      max-width: 800px;
    }

    .clock-container {
      background: var(--glass-color);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 24px;
      padding: 40px;
      margin-bottom: 30px;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px var(--shadow-color);
      transition: all 0.3s ease;
    }

    .clock-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 40px var(--shadow-color);
    }

    #clock {
      font-size: clamp(3rem, 8vw, 6rem);
      font-weight: 700;
      letter-spacing: 0.05em;
      margin-bottom: 15px;
      cursor: pointer;
      user-select: none;
      color: var(--neon-color);
      text-shadow: 0 0 20px var(--neon-color);
      transition: all 0.3s ease;
      font-variant-numeric: tabular-nums;
    }

    #clock:hover {
      transform: scale(1.02);
      text-shadow: 0 0 30px var(--neon-color);
    }

    #date {
      font-size: clamp(1rem, 3vw, 1.5rem);
      font-weight: 500;
      opacity: 0.8;
      margin-bottom: 10px;
    }

    #format-hint {
      font-size: 0.9rem;
      opacity: 0.6;
      font-weight: 400;
    }

    .weather-container {
      background: var(--glass-color);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 25px;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      box-shadow: 0 8px 32px var(--shadow-color);
      transition: all 0.3s ease;
      min-height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .weather-content {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .weather-temp {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--neon-color);
      text-shadow: 0 0 15px var(--neon-color);
    }

    .weather-details {
      text-align: left;
    }

    .weather-condition {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .weather-location {
      font-size: 0.9rem;
      opacity: 0.7;
    }

    .loading {
      opacity: 0.6;
      font-style: italic;
    }

    .error {
      color: #ff3b30;
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .controls {
        top: 15px;
        right: 15px;
        gap: 10px;
      }

      .control-btn {
        padding: 8px 12px;
        font-size: 12px;
      }

      .clock-container, .weather-container {
        margin: 0 15px;
        padding: 25px 20px;
      }

      .weather-content {
        flex-direction: column;
        gap: 15px;
      }

      .weather-details {
        text-align: center;
      }
    }

    /* Smooth transitions for theme changes */
    * {
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="controls">
    <button class="control-btn" id="theme-toggle" aria-label="Toggle theme">🌙</button>
    <button class="control-btn" id="lang-toggle" aria-label="Toggle language">EN</button>
  </div>

  <div class="main-container">
    <div class="clock-container">
      <div id="clock">--:--:--</div>
      <div id="date" data-en="Loading date..." data-es="Cargando fecha...">Loading date...</div>
      <div id="format-hint" data-en="(Click time to change format)" data-es="(Haz clic en la hora para cambiar formato)">(Click time to change format)</div>
    </div>

    <div class="weather-container">
      <div class="weather-content" id="weather-content">
        <div class="loading" data-en="Loading weather..." data-es="Cargando clima...">Loading weather...</div>
      </div>
    </div>
  </div>

  <script>
    // Application state
    let state = {
      is24Hour: true,
      currentLang: 'en',
      theme: 'light',
      weatherData: null,
      location: null
    };

    // Language translations
    const translations = {
      en: {
        'Loading date...': 'Loading date...',
        'Loading weather...': 'Loading weather...',
        '(Click time to change format)': '(Click time to change format)',
        'Weather unavailable': 'Weather unavailable',
        'Location access denied': 'Location access denied',
        'Weather service error': 'Weather service error',
        'Monday': 'Monday',
        'Tuesday': 'Tuesday',
        'Wednesday': 'Wednesday',
        'Thursday': 'Thursday',
        'Friday': 'Friday',
        'Saturday': 'Saturday',
        'Sunday': 'Sunday',
        'January': 'January',
        'February': 'February',
        'March': 'March',
        'April': 'April',
        'May': 'May',
        'June': 'June',
        'July': 'July',
        'August': 'August',
        'September': 'September',
        'October': 'October',
        'November': 'November',
        'December': 'December'
      },
      es: {
        'Loading date...': 'Cargando fecha...',
        'Loading weather...': 'Cargando clima...',
        '(Click time to change format)': '(Haz clic en la hora para cambiar formato)',
        'Weather unavailable': 'Clima no disponible',
        'Location access denied': 'Acceso a ubicación denegado',
        'Weather service error': 'Error del servicio meteorológico',
        'Monday': 'Lunes',
        'Tuesday': 'Martes',
        'Wednesday': 'Miércoles',
        'Thursday': 'Jueves',
        'Friday': 'Viernes',
        'Saturday': 'Sábado',
        'Sunday': 'Domingo',
        'January': 'Enero',
        'February': 'Febrero',
        'March': 'Marzo',
        'April': 'Abril',
        'May': 'Mayo',
        'June': 'Junio',
        'July': 'Julio',
        'August': 'Agosto',
        'September': 'Septiembre',
        'October': 'Octubre',
        'November': 'Noviembre',
        'December': 'Diciembre'
      }
    };

    // Initialize application
    function init() {
      loadSettings();
      setupEventListeners();
      updateClock();
      updateLanguage();
      requestLocation();

      // Update clock every second
      setInterval(updateClock, 1000);
    }

    // Load settings from localStorage
    function loadSettings() {
      const savedTheme = localStorage.getItem('clockTheme') || 'light';
      const savedLang = localStorage.getItem('clockLang') || 'en';
      const saved24Hour = localStorage.getItem('clock24Hour');

      state.theme = savedTheme;
      state.currentLang = savedLang;
      state.is24Hour = saved24Hour !== null ? JSON.parse(saved24Hour) : true;

      applyTheme(savedTheme);
      document.getElementById('lang-toggle').textContent = savedLang.toUpperCase();
    }

    // Save settings to localStorage
    function saveSettings() {
      localStorage.setItem('clockTheme', state.theme);
      localStorage.setItem('clockLang', state.currentLang);
      localStorage.setItem('clock24Hour', JSON.stringify(state.is24Hour));
    }

    // Setup event listeners
    function setupEventListeners() {
      document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
      document.getElementById('lang-toggle').addEventListener('click', toggleLanguage);
      document.getElementById('clock').addEventListener('click', toggleTimeFormat);
    }

    // Theme management
    function toggleTheme() {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
      applyTheme(state.theme);
      saveSettings();
    }

    function applyTheme(theme) {
      document.documentElement.setAttribute('data-theme', theme);
      const themeButton = document.getElementById('theme-toggle');
      themeButton.textContent = theme === 'light' ? '🌙' : '☀️';
      themeButton.setAttribute('aria-label', `Switch to ${theme === 'light' ? 'dark' : 'light'} theme`);
    }

    // Language management
    function toggleLanguage() {
      state.currentLang = state.currentLang === 'en' ? 'es' : 'en';
      document.getElementById('lang-toggle').textContent = state.currentLang.toUpperCase();
      document.documentElement.lang = state.currentLang;
      updateLanguage();
      updateClock(); // Refresh date display
      saveSettings();
    }

    function updateLanguage() {
      document.querySelectorAll('[data-en]').forEach(element => {
        const key = element.getAttribute(`data-${state.currentLang}`);
        if (key && translations[state.currentLang][key]) {
          element.textContent = translations[state.currentLang][key];
        }
      });
    }

    function translate(key) {
      return translations[state.currentLang][key] || key;
    }
    // Clock functionality
    function updateClock() {
      const now = new Date();
      let hours = now.getHours();
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      let period = '';
      if (!state.is24Hour) {
        period = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12;
      }
      hours = String(hours).padStart(2, '0');

      const timeString = `${hours}:${minutes}:${seconds}${!state.is24Hour ? ' ' + period : ''}`;
      document.getElementById('clock').textContent = timeString;

      // Update date
      updateDate(now);
    }

    function updateDate(date) {
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];

      const dayName = translate(days[date.getDay()]);
      const monthName = translate(months[date.getMonth()]);
      const day = date.getDate();
      const year = date.getFullYear();

      const dateString = `${dayName}, ${monthName} ${day}, ${year}`;
      document.getElementById('date').textContent = dateString;
    }

    function toggleTimeFormat() {
      state.is24Hour = !state.is24Hour;
      updateClock();
      saveSettings();
    }

    // Geolocation and Weather
    function requestLocation() {
      if (!navigator.geolocation) {
        showWeatherError(translate('Weather unavailable'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        position => {
          state.location = {
            lat: position.coords.latitude,
            lon: position.coords.longitude
          };
          fetchWeather();
        },
        error => {
          console.error('Geolocation error:', error);
          showWeatherError(translate('Location access denied'));
        },
        {
          timeout: 10000,
          enableHighAccuracy: false
        }
      );
    }

    async function fetchWeather() {
      if (!state.location) return;

      try {
        // First, get the weather station/grid point from coordinates
        const pointResponse = await fetch(
          `https://api.weather.gov/points/${state.location.lat},${state.location.lon}`
        );

        if (!pointResponse.ok) {
          throw new Error('Failed to get weather point data');
        }

        const pointData = await pointResponse.json();

        // Get current weather from the forecast office
        const forecastResponse = await fetch(pointData.properties.forecast);

        if (!forecastResponse.ok) {
          throw new Error('Failed to get weather forecast');
        }

        const forecastData = await forecastResponse.json();
        const currentPeriod = forecastData.properties.periods[0];

        // Get current conditions (this might not always be available)
        let currentTemp = null;
        try {
          const stationsResponse = await fetch(pointData.properties.observationStations);
          const stationsData = await stationsResponse.json();

          if (stationsData.features && stationsData.features.length > 0) {
            const stationUrl = stationsData.features[0].id;
            const observationResponse = await fetch(`${stationUrl}/observations/latest`);
            const observationData = await observationResponse.json();

            if (observationData.properties && observationData.properties.temperature.value) {
              // Convert Celsius to Fahrenheit
              const tempC = observationData.properties.temperature.value;
              currentTemp = Math.round((tempC * 9/5) + 32);
            }
          }
        } catch (obsError) {
          console.log('Could not get current observations, using forecast data');
        }

        state.weatherData = {
          temperature: currentTemp || currentPeriod.temperature,
          condition: currentPeriod.shortForecast,
          location: pointData.properties.relativeLocation?.properties?.city || 'Unknown Location'
        };

        displayWeather();

      } catch (error) {
        console.error('Weather fetch error:', error);
        showWeatherError(translate('Weather service error'));
      }
    }

    function displayWeather() {
      if (!state.weatherData) return;

      const weatherContent = document.getElementById('weather-content');
      weatherContent.innerHTML = `
        <div class="weather-temp">${state.weatherData.temperature}°F</div>
        <div class="weather-details">
          <div class="weather-condition">${state.weatherData.condition}</div>
          <div class="weather-location">${state.weatherData.location}</div>
        </div>
      `;
    }

    function showWeatherError(message) {
      const weatherContent = document.getElementById('weather-content');
      weatherContent.innerHTML = `<div class="error">${message}</div>`;
    }

    // Initialize the application when DOM is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  </script>
</body>
</html>