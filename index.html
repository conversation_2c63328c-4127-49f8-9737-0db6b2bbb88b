<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Clock Interface</title>
  <style>
    :root {
      /* Customizable theme colors */
      --bg-light-custom: #ffffff;
      --text-light-custom: #1d1d1f;
      --bg-dark-custom: #000000;
      --text-dark-custom: #f5f5f7;
      --web-background: #ffffff;

      /* Current theme colors */
      --bg-color: var(--bg-light-custom);
      --text-color: var(--text-light-custom);
      --accent-color: #007aff;
      --glass-color: rgba(255, 255, 255, 0);
      --shadow-color: rgba(0, 0, 0, 0.1);

      /* Customizable properties */
      --glass-opacity: 0;
      --border-radius: 24px;
      --blur-intensity: 20px;
      --clock-font-size: clamp(3rem, 8vw, 6rem);
      --date-font-size: clamp(1rem, 3vw, 1.5rem);
      --weather-font-size: 2.5rem;

      /* Neon effects (disabled by default) */
      --clock-neon-enabled: 0;
      --clock-neon-color: #00d4ff;
      --clock-neon-intensity: 20px;
      --clock-neon-opacity: 1;

      --weather-neon-enabled: 0;
      --weather-neon-color: #00d4ff;
      --weather-neon-intensity: 15px;
      --weather-neon-opacity: 1;

      --container-neon-enabled: 0;
      --container-neon-color: #00d4ff;
      --container-neon-intensity: 10px;
      --container-neon-opacity: 0.5;

      /* Layout properties */
      --main-justify: center;
      --main-align: center;
      --clock-container-display: block;
      --weather-container-display: flex;
      --date-display: block;
      --weather-location-display: block;
      --weather-details-display: block;
      --weather-wind-display: none;
      --weather-humidity-display: none;
      --weather-pressure-display: none;
      --weather-visibility-display: none;
      --weather-rain-chance-display: none;
    }

    [data-theme="dark"] {
      --bg-color: #000000;
      --text-color: #ffffff;
      --web-background: #000000;
      --accent-color: #ffffff;
      --glass-color: rgba(0, 0, 0, 0.8);
      --shadow-color: rgba(255, 255, 255, 0.1);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
      background: var(--web-background);
      color: var(--text-color);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: var(--main-align);
      justify-content: var(--main-justify);
      transition: all 0.3s ease;
      overflow: hidden;
      margin: 0;
      padding: 0;
    }

    /* Hide scrollbars */
    * {
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    *::-webkit-scrollbar {
      display: none; /* WebKit */
    }

    .controls {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .control-btn {
      background: var(--glass-color);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 10px 15px;
      color: var(--text-color);
      cursor: pointer;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 15px var(--shadow-color);
    }

    [data-theme="dark"] .control-btn {
      background: rgba(0, 0, 0, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: #ffffff;
    }

    .control-btn:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px var(--shadow-color);
    }

    .main-container {
      text-align: center;
      padding: 0;
      width: 100%;
      max-width: 800px;
    }

    .clock-container {
      background: rgba(255, 255, 255, var(--glass-opacity));
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      padding: 40px;
      margin-bottom: 30px;
      backdrop-filter: blur(var(--blur-intensity));
      -webkit-backdrop-filter: blur(var(--blur-intensity));
      box-shadow: 0 8px 32px var(--shadow-color);
      transition: all 0.3s ease;
      display: var(--clock-container-display);
    }

    .clock-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 40px var(--shadow-color);
    }

    #clock {
      font-size: var(--clock-font-size);
      font-weight: 700;
      letter-spacing: 0.05em;
      margin-bottom: 15px;
      cursor: pointer;
      user-select: none;
      color: var(--text-color);
      text-shadow: 0 0 0 transparent;
      transition: all 0.3s ease;
      font-variant-numeric: tabular-nums;
    }

    #clock:hover {
      transform: scale(1.02);
      text-shadow: 0 0 0 transparent;
    }

    #date {
      font-size: var(--date-font-size);
      font-weight: 500;
      opacity: 0.8;
      margin-bottom: 10px;
      display: var(--date-display);
    }

    #format-hint {
      font-size: 0.9rem;
      opacity: 0.6;
      font-weight: 400;
    }

    .weather-container {
      background: rgba(255, 255, 255, var(--glass-opacity));
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      padding: 25px;
      backdrop-filter: blur(var(--blur-intensity));
      -webkit-backdrop-filter: blur(var(--blur-intensity));
      box-shadow: 0 8px 32px var(--shadow-color);
      transition: all 0.3s ease;
      min-height: 120px;
      display: var(--weather-container-display);
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }

    .weather-content {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .weather-temp {
      font-size: var(--weather-font-size);
      font-weight: 700;
      color: var(--text-color);
      text-shadow: 0 0 0 transparent;
    }

    .weather-details {
      text-align: left;
    }

    .weather-condition {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .weather-location {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 10px;
      display: var(--weather-location-display);
    }

    .weather-main-info {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 15px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .weather-details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 12px;
      width: 100%;
      margin-top: 15px;
      padding: 0 10px;
    }

    .weather-detail-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 12px 8px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      min-height: 70px;
    }

    .weather-detail-item:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .weather-detail-item {
      background: rgba(0, 0, 0, 0.3);
      border-color: rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] .weather-detail-item:hover {
      background: rgba(0, 0, 0, 0.5);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    }

    .weather-detail-icon {
      width: 24px;
      height: 24px;
      object-fit: contain;
      margin-bottom: 6px;
    }

    [data-theme="dark"] .weather-detail-icon {
      filter: brightness(1.2) contrast(1.1);
    }

    .weather-detail-value {
      font-weight: 600;
      font-size: 0.85rem;
      color: var(--text-color);
    }

    .weather-wind {
      display: var(--weather-wind-display);
    }

    .weather-humidity {
      display: var(--weather-humidity-display);
    }

    .weather-pressure {
      display: var(--weather-pressure-display);
    }

    .weather-visibility {
      display: var(--weather-visibility-display);
    }

    .weather-rain-chance {
      display: var(--weather-rain-chance-display);
    }

    .rain-chance-simple {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-top: 10px;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .loading {
      opacity: 0.6;
      font-style: italic;
    }

    .error {
      color: #ff3b30;
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .controls {
        top: 15px;
        right: 15px;
        gap: 10px;
      }

      .control-btn {
        padding: 8px 12px;
        font-size: 12px;
      }

      .clock-container, .weather-container {
        margin: 0 15px;
        padding: 25px 20px;
      }

      .weather-content {
        flex-direction: column;
        gap: 15px;
      }

      .weather-details {
        text-align: center;
      }
    }

    /* Weather Icons */
    .weather-icon {
      margin-right: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .weather-svg-icon {
      width: 48px;
      height: 48px;
      object-fit: contain;
      transition: all 0.3s ease;
    }

    .weather-svg-icon:hover {
      transform: scale(1.1);
    }

    /* Dark mode filter for SVG icons */
    [data-theme="dark"] .weather-svg-icon {
      filter: brightness(1.2) contrast(1.1);
    }

    .rain-chance-simple {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-top: 10px;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .rain-chance-icon {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }

    [data-theme="dark"] .rain-chance-icon {
      filter: brightness(1.2) contrast(1.1);
    }

    /* Settings Panel */
    .settings-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      z-index: 2000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .settings-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .settings-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.9);
      width: 90%;
      max-width: 650px;
      max-height: 85vh;
      background: #ffffff;
      border: none;
      border-radius: 20px;
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      transition: all 0.3s ease;
    }

    [data-theme="dark"] .settings-panel {
      background: #000000;
      border: 2px solid #333333;
      box-shadow: 0 25px 80px rgba(255, 255, 255, 0.1);
    }

    .settings-overlay.active .settings-panel {
      transform: translate(-50%, -50%) scale(1);
    }

    .settings-header {
      padding: 25px 30px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    [data-theme="dark"] .settings-header {
      background: #111111;
      border-bottom: 1px solid #333333;
    }

    .settings-title {
      font-size: 1.6rem;
      font-weight: 700;
      color: #2c3e50;
      margin: 0;
    }

    [data-theme="dark"] .settings-title {
      color: #ffffff;
    }

    .settings-close {
      background: #e74c3c;
      border: none;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .settings-close:hover {
      background: #c0392b;
      transform: scale(1.1);
    }

    .settings-content {
      padding: 0;
      max-height: calc(85vh - 80px);
      overflow-y: auto;
    }

    .settings-section {
      padding: 25px 30px;
      border-bottom: 1px solid #f1f3f4;
    }

    [data-theme="dark"] .settings-section {
      border-bottom: 1px solid #222222;
    }

    .settings-section:last-child {
      border-bottom: none;
    }

    .settings-section-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: #3498db;
      margin-bottom: 20px;
      padding-bottom: 8px;
      border-bottom: 2px solid #3498db;
      display: inline-block;
    }

    [data-theme="dark"] .settings-section-title {
      color: #5dade2;
      border-bottom-color: #5dade2;
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 18px;
      padding: 12px 0;
    }

    .setting-item:last-child {
      margin-bottom: 0;
    }

    .setting-label {
      font-weight: 500;
      color: #2c3e50;
      flex: 1;
      font-size: 14px;
    }

    [data-theme="dark"] .setting-label {
      color: #ecf0f1;
    }

    .setting-control {
      flex: 0 0 auto;
      margin-left: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    /* Control Styles */
    .slider {
      width: 140px;
      height: 8px;
      border-radius: 4px;
      background: #e9ecef;
      outline: none;
      -webkit-appearance: none;
      appearance: none;
      cursor: pointer;
    }

    [data-theme="dark"] .slider {
      background: #333333;
    }

    .slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #3498db;
      cursor: pointer;
      border: 2px solid #ffffff;
      box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
      transition: all 0.2s ease;
    }

    .slider::-webkit-slider-thumb:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.5);
    }

    [data-theme="dark"] .slider::-webkit-slider-thumb {
      border-color: #000000;
    }

    .slider::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #3498db;
      cursor: pointer;
      border: 2px solid #ffffff;
      box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
    }

    .color-picker {
      width: 60px;
      height: 35px;
      border: 2px solid #ddd;
      border-radius: 8px;
      cursor: pointer;
      background: none;
      transition: all 0.2s ease;
    }

    .color-picker:hover {
      border-color: #3498db;
      transform: scale(1.05);
    }

    [data-theme="dark"] .color-picker {
      border-color: #555555;
    }

    [data-theme="dark"] .color-picker:hover {
      border-color: #5dade2;
    }

    .toggle-switch {
      position: relative;
      width: 54px;
      height: 30px;
      background: #bdc3c7;
      border-radius: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .toggle-switch:hover {
      transform: scale(1.05);
    }

    .toggle-switch.active {
      background: #27ae60;
      box-shadow: 0 0 20px rgba(39, 174, 96, 0.3);
    }

    [data-theme="dark"] .toggle-switch {
      background: #444444;
    }

    [data-theme="dark"] .toggle-switch.active {
      background: #2ecc71;
    }

    .toggle-switch::after {
      content: '';
      position: absolute;
      top: 3px;
      left: 3px;
      width: 22px;
      height: 22px;
      background: white;
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    .toggle-switch.active::after {
      transform: translateX(24px);
    }

    .slider-value {
      font-size: 12px;
      font-weight: 600;
      color: #7f8c8d;
      min-width: 45px;
      text-align: center;
    }

    [data-theme="dark"] .slider-value {
      color: #bdc3c7;
    }

    .select-control {
      background: #ffffff;
      border: 2px solid #ddd;
      border-radius: 8px;
      padding: 10px 15px;
      color: #2c3e50;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 120px;
    }

    .select-control:hover {
      border-color: #3498db;
    }

    .select-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    [data-theme="dark"] .select-control {
      background: #222222;
      border-color: #555555;
      color: #ffffff;
    }

    [data-theme="dark"] .select-control:hover {
      border-color: #5dade2;
    }

    [data-theme="dark"] .select-control:focus {
      border-color: #5dade2;
      box-shadow: 0 0 0 3px rgba(93, 173, 226, 0.1);
    }

    .settings-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      padding: 25px 30px;
      background: #f8f9fa;
      border-top: 1px solid #e9ecef;
    }

    [data-theme="dark"] .settings-actions {
      background: #111111;
      border-top: 1px solid #333333;
    }

    .settings-btn {
      background: #ffffff;
      border: 2px solid #bdc3c7;
      border-radius: 12px;
      padding: 12px 24px;
      color: #2c3e50;
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;
      min-width: 120px;
    }

    .settings-btn:hover {
      background: #ecf0f1;
      border-color: #95a5a6;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] .settings-btn {
      background: #222222;
      border-color: #555555;
      color: #ffffff;
    }

    [data-theme="dark"] .settings-btn:hover {
      background: #333333;
      border-color: #777777;
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
    }

    .settings-btn.primary {
      background: #3498db;
      border-color: #3498db;
      color: white;
    }

    .settings-btn.primary:hover {
      background: #2980b9;
      border-color: #2980b9;
      box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .settings-btn.reset {
      background: #e74c3c;
      border-color: #e74c3c;
      color: white;
    }

    .settings-btn.reset:hover {
      background: #c0392b;
      border-color: #c0392b;
      box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    /* Smooth transitions for theme changes */
    * {
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="controls">
    <button class="control-btn" id="settings-toggle" aria-label="Open settings">⚙️</button>
  </div>

  <div class="main-container">
    <div class="clock-container">
      <div id="clock">--:--:--</div>
      <div id="date" data-en="Loading date..." data-es="Cargando fecha...">Loading date...</div>
      <div id="format-hint" data-en="(Click time to change format)" data-es="(Haz clic en la hora para cambiar formato)">(Click time to change format)</div>
    </div>

    <div class="weather-container">
      <div class="weather-content" id="weather-content">
        <div class="loading" data-en="Loading weather..." data-es="Cargando clima...">Cargando clima...</div>
      </div>
    </div>
  </div>

  <!-- Settings Panel -->
  <div class="settings-overlay" id="settings-overlay">
    <div class="settings-panel">
      <div class="settings-header">
        <h2 class="settings-title" data-en="Settings" data-es="Configuración">Settings</h2>
        <button class="settings-close" id="settings-close" aria-label="Close settings">×</button>
      </div>
      <div class="settings-content">
        <!-- Theme Controls -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Theme & Language" data-es="Tema e Idioma">Tema e Idioma</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Theme Mode" data-es="Modo de Tema">Modo de Tema</label>
            <div class="setting-control">
              <button class="settings-btn" id="theme-toggle-settings" aria-label="Toggle theme">🌙 Oscuro</button>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Language" data-es="Idioma">Idioma</label>
            <div class="setting-control">
              <button class="settings-btn" id="lang-toggle-settings" aria-label="Toggle language">ES</button>
            </div>
          </div>
        </div>

        <!-- Color Customization -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Colors & Background" data-es="Colores y Fondo">Colores y Fondo</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Web Background" data-es="Fondo de la Web">Fondo de la Web</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="web-background" value="#ffffff">
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Light Mode Text Color" data-es="Color de Texto Modo Claro">Color de Texto Modo Claro</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="text-light-color" value="#1d1d1f">
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Light Mode Background" data-es="Fondo Modo Claro">Fondo Modo Claro</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="bg-light-color" value="#ffffff">
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Dark Mode Text Color" data-es="Color de Texto Modo Oscuro">Color de Texto Modo Oscuro</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="text-dark-color" value="#f5f5f7">
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Dark Mode Background" data-es="Fondo Modo Oscuro">Fondo Modo Oscuro</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="bg-dark-color" value="#000000">
            </div>
          </div>
        </div>

        <!-- Visual Customization -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Glass Effects" data-es="Efectos de Cristal">Efectos de Cristal</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Glass Opacity" data-es="Opacidad del Cristal">Opacidad del Cristal</label>
            <div class="setting-control">
              <input type="range" class="slider" id="glass-opacity" min="0" max="100" value="0">
              <span class="slider-value" id="glass-opacity-value">0%</span>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Border Radius" data-es="Radio del Borde">Radio del Borde</label>
            <div class="setting-control">
              <input type="range" class="slider" id="border-radius" min="0" max="50" value="24">
              <span class="slider-value" id="border-radius-value">24px</span>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Blur Intensity" data-es="Intensidad del Desenfoque">Intensidad del Desenfoque</label>
            <div class="setting-control">
              <input type="range" class="slider" id="blur-intensity" min="0" max="50" value="20">
              <span class="slider-value" id="blur-intensity-value">20px</span>
            </div>
          </div>
        </div>

        <!-- Neon Effects -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Neon Effects" data-es="Efectos Neón">Efectos Neón</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Clock Neon Effect" data-es="Efecto Neón del Reloj">Efecto Neón del Reloj</label>
            <div class="setting-control">
              <div class="toggle-switch" id="clock-neon-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Clock Neon Color" data-es="Color Neón del Reloj">Color Neón del Reloj</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="clock-neon-color" value="#00d4ff">
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Clock Neon Intensity" data-es="Intensidad Neón del Reloj">Intensidad Neón del Reloj</label>
            <div class="setting-control">
              <input type="range" class="slider" id="clock-neon-intensity" min="0" max="50" value="20">
              <span class="slider-value" id="clock-neon-intensity-value">20px</span>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Weather Neon Effect" data-es="Efecto Neón del Clima">Efecto Neón del Clima</label>
            <div class="setting-control">
              <div class="toggle-switch" id="weather-neon-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Weather Neon Color" data-es="Color Neón del Clima">Color Neón del Clima</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="weather-neon-color" value="#00d4ff">
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Container Neon Effect" data-es="Efecto Neón de Contenedores">Efecto Neón de Contenedores</label>
            <div class="setting-control">
              <div class="toggle-switch" id="container-neon-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Container Neon Color" data-es="Color Neón de Contenedores">Color Neón de Contenedores</label>
            <div class="setting-control">
              <input type="color" class="color-picker" id="container-neon-color" value="#00d4ff">
            </div>
          </div>
        </div>

        <!-- Font Sizes -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Font Sizes" data-es="Tamaños de Fuente">Tamaños de Fuente</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Clock Size" data-es="Tamaño del Reloj">Tamaño del Reloj</label>
            <div class="setting-control">
              <input type="range" class="slider" id="clock-size" min="2" max="10" value="6" step="0.5">
              <span class="slider-value" id="clock-size-value">6rem</span>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Date Size" data-es="Tamaño de la Fecha">Tamaño de la Fecha</label>
            <div class="setting-control">
              <input type="range" class="slider" id="date-size" min="0.8" max="3" value="1.5" step="0.1">
              <span class="slider-value" id="date-size-value">1.5rem</span>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Weather Size" data-es="Tamaño del Clima">Tamaño del Clima</label>
            <div class="setting-control">
              <input type="range" class="slider" id="weather-size" min="1" max="5" value="2.5" step="0.1">
              <span class="slider-value" id="weather-size-value">2.5rem</span>
            </div>
          </div>
        </div>

        <!-- Layout & Visibility -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Layout & Visibility" data-es="Diseño y Visibilidad">Layout & Visibility</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Clock Container" data-es="Mostrar Contenedor del Reloj">Show Clock Container</label>
            <div class="setting-control">
              <div class="toggle-switch active" id="clock-container-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Weather Container" data-es="Mostrar Contenedor del Clima">Mostrar Contenedor del Clima</label>
            <div class="setting-control">
              <div class="toggle-switch active" id="weather-container-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Date" data-es="Mostrar Fecha">Show Date</label>
            <div class="setting-control">
              <div class="toggle-switch active" id="date-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Weather Location" data-es="Mostrar Ubicación del Clima">Show Weather Location</label>
            <div class="setting-control">
              <div class="toggle-switch active" id="weather-location-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Vertical Position" data-es="Posición Vertical">Vertical Position</label>
            <div class="setting-control">
              <select class="select-control" id="vertical-position">
                <option value="flex-start" data-en="Top" data-es="Arriba">Top</option>
                <option value="center" selected data-en="Center" data-es="Centro">Center</option>
                <option value="flex-end" data-en="Bottom" data-es="Abajo">Bottom</option>
              </select>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Horizontal Position" data-es="Posición Horizontal">Horizontal Position</label>
            <div class="setting-control">
              <select class="select-control" id="horizontal-position">
                <option value="flex-start" data-en="Left" data-es="Izquierda">Left</option>
                <option value="center" selected data-en="Center" data-es="Centro">Center</option>
                <option value="flex-end" data-en="Right" data-es="Derecha">Right</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Weather Information -->
        <div class="settings-section">
          <h3 class="settings-section-title" data-en="Weather Information" data-es="Información del Clima">Información del Clima</h3>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Wind Speed" data-es="Mostrar Velocidad del Viento">Mostrar Velocidad del Viento</label>
            <div class="setting-control">
              <div class="toggle-switch" id="weather-wind-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Humidity" data-es="Mostrar Humedad">Mostrar Humedad</label>
            <div class="setting-control">
              <div class="toggle-switch" id="weather-humidity-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Pressure" data-es="Mostrar Presión">Mostrar Presión</label>
            <div class="setting-control">
              <div class="toggle-switch" id="weather-pressure-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Visibility" data-es="Mostrar Visibilidad">Mostrar Visibilidad</label>
            <div class="setting-control">
              <div class="toggle-switch" id="weather-visibility-toggle"></div>
            </div>
          </div>

          <div class="setting-item">
            <label class="setting-label" data-en="Show Rain Chance" data-es="Mostrar Probabilidad de Lluvia">Mostrar Probabilidad de Lluvia</label>
            <div class="setting-control">
              <div class="toggle-switch" id="weather-rain-chance-toggle"></div>
            </div>
          </div>
        </div>

        <div class="settings-actions">
          <button class="settings-btn reset" id="reset-settings" data-en="Reset to Defaults" data-es="Restablecer Predeterminados">Restablecer Predeterminados</button>
          <button class="settings-btn primary" id="save-settings" data-en="Save Settings" data-es="Guardar Configuración">Guardar Configuración</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Application state
    let state = {
      is24Hour: true,
      currentLang: 'es',
      theme: 'light',
      weatherData: null,
      location: null,
      settings: {
        // Colors
        webBackground: '#ffffff',
        textLightColor: '#1d1d1f',
        bgLightColor: '#ffffff',
        textDarkColor: '#f5f5f7',
        bgDarkColor: '#000000',

        // Glass effects
        glassOpacity: 0,
        borderRadius: 24,
        blurIntensity: 20,

        // Font sizes
        clockSize: 6,
        dateSize: 1.5,
        weatherSize: 2.5,

        // Neon effects
        clockNeonEnabled: false,
        clockNeonColor: '#00d4ff',
        clockNeonIntensity: 20,
        clockNeonOpacity: 1,

        weatherNeonEnabled: false,
        weatherNeonColor: '#00d4ff',
        weatherNeonIntensity: 15,
        weatherNeonOpacity: 1,

        containerNeonEnabled: false,
        containerNeonColor: '#00d4ff',
        containerNeonIntensity: 10,
        containerNeonOpacity: 0.5,

        // Layout
        showClockContainer: true,
        showWeatherContainer: true,
        showDate: true,
        showWeatherLocation: true,
        verticalPosition: 'center',
        horizontalPosition: 'center',

        // Weather details
        showWeatherWind: false,
        showWeatherHumidity: false,
        showWeatherPressure: false,
        showWeatherVisibility: false,
        showWeatherRainChance: false
      }
    };

    // Language translations
    const translations = {
      en: {
        'Loading date...': 'Loading date...',
        'Loading weather...': 'Loading weather...',
        '(Click time to change format)': '(Click time to change format)',
        'Weather unavailable': 'Weather unavailable',
        'Location access denied': 'Location access denied',
        'Weather service error': 'Weather service error',
        'Monday': 'Monday',
        'Tuesday': 'Tuesday',
        'Wednesday': 'Wednesday',
        'Thursday': 'Thursday',
        'Friday': 'Friday',
        'Saturday': 'Saturday',
        'Sunday': 'Sunday',
        'January': 'January',
        'February': 'February',
        'March': 'March',
        'April': 'April',
        'May': 'May',
        'June': 'June',
        'July': 'July',
        'August': 'August',
        'September': 'September',
        'October': 'October',
        'November': 'November',
        'December': 'December',
        'Settings': 'Settings',
        'Visual Customization': 'Visual Customization',
        'Neon Accent Color': 'Neon Accent Color',
        'Glass Opacity': 'Glass Opacity',
        'Border Radius': 'Border Radius',
        'Blur Intensity': 'Blur Intensity',
        'Font Sizes': 'Font Sizes',
        'Clock Size': 'Clock Size',
        'Date Size': 'Date Size',
        'Weather Size': 'Weather Size',
        'Layout & Visibility': 'Layout & Visibility',
        'Show Clock Container': 'Show Clock Container',
        'Show Weather Container': 'Show Weather Container',
        'Show Date': 'Show Date',
        'Show Weather Location': 'Show Weather Location',
        'Vertical Position': 'Vertical Position',
        'Horizontal Position': 'Horizontal Position',
        'Top': 'Top',
        'Center': 'Center',
        'Bottom': 'Bottom',
        'Left': 'Left',
        'Right': 'Right',
        'Reset to Defaults': 'Reset to Defaults',
        'Save Settings': 'Save Settings',
        'Theme & Language': 'Theme & Language',
        'Theme Mode': 'Theme Mode',
        'Language': 'Language',
        'Colors & Background': 'Colors & Background',
        'Web Background': 'Web Background',
        'Light Mode Text Color': 'Light Mode Text Color',
        'Light Mode Background': 'Light Mode Background',
        'Dark Mode Text Color': 'Dark Mode Text Color',
        'Dark Mode Background': 'Dark Mode Background',
        'Glass Effects': 'Glass Effects',
        'Neon Effects': 'Neon Effects',
        'Clock Neon Effect': 'Clock Neon Effect',
        'Clock Neon Color': 'Clock Neon Color',
        'Clock Neon Intensity': 'Clock Neon Intensity',
        'Weather Neon Effect': 'Weather Neon Effect',
        'Weather Neon Color': 'Weather Neon Color',
        'Container Neon Effect': 'Container Neon Effect',
        'Container Neon Color': 'Container Neon Color',
        'Weather Information': 'Weather Information',
        'Show Wind Speed': 'Show Wind Speed',
        'Show Humidity': 'Show Humidity',
        'Show Pressure': 'Show Pressure',
        'Show Visibility': 'Show Visibility',
        'Show Rain Chance': 'Show Rain Chance',
        'Wind': 'Wind',
        'Humidity': 'Humidity',
        'Pressure': 'Pressure',
        'Visibility': 'Visibility',
        'Rain Chance': 'Rain Chance'
      },
      es: {
        'Loading date...': 'Cargando fecha...',
        'Loading weather...': 'Cargando clima...',
        '(Click time to change format)': '(Haz clic en la hora para cambiar formato)',
        'Weather unavailable': 'Clima no disponible',
        'Location access denied': 'Acceso a ubicación denegado',
        'Weather service error': 'Error del servicio meteorológico',
        'Monday': 'Lunes',
        'Tuesday': 'Martes',
        'Wednesday': 'Miércoles',
        'Thursday': 'Jueves',
        'Friday': 'Viernes',
        'Saturday': 'Sábado',
        'Sunday': 'Domingo',
        'January': 'Enero',
        'February': 'Febrero',
        'March': 'Marzo',
        'April': 'Abril',
        'May': 'Mayo',
        'June': 'Junio',
        'July': 'Julio',
        'August': 'Agosto',
        'September': 'Septiembre',
        'October': 'Octubre',
        'November': 'Noviembre',
        'December': 'Diciembre',
        'Settings': 'Configuración',
        'Visual Customization': 'Personalización Visual',
        'Neon Accent Color': 'Color de Acento Neón',
        'Glass Opacity': 'Opacidad del Cristal',
        'Border Radius': 'Radio del Borde',
        'Blur Intensity': 'Intensidad del Desenfoque',
        'Font Sizes': 'Tamaños de Fuente',
        'Clock Size': 'Tamaño del Reloj',
        'Date Size': 'Tamaño de la Fecha',
        'Weather Size': 'Tamaño del Clima',
        'Layout & Visibility': 'Diseño y Visibilidad',
        'Show Clock Container': 'Mostrar Contenedor del Reloj',
        'Show Weather Container': 'Mostrar Contenedor del Clima',
        'Show Date': 'Mostrar Fecha',
        'Show Weather Location': 'Mostrar Ubicación del Clima',
        'Vertical Position': 'Posición Vertical',
        'Horizontal Position': 'Posición Horizontal',
        'Top': 'Arriba',
        'Center': 'Centro',
        'Bottom': 'Abajo',
        'Left': 'Izquierda',
        'Right': 'Derecha',
        'Reset to Defaults': 'Restablecer Predeterminados',
        'Save Settings': 'Guardar Configuración',
        'Theme & Language': 'Tema e Idioma',
        'Theme Mode': 'Modo de Tema',
        'Language': 'Idioma',
        'Colors & Background': 'Colores y Fondo',
        'Web Background': 'Fondo de la Web',
        'Light Mode Text Color': 'Color de Texto Modo Claro',
        'Light Mode Background': 'Fondo Modo Claro',
        'Dark Mode Text Color': 'Color de Texto Modo Oscuro',
        'Dark Mode Background': 'Fondo Modo Oscuro',
        'Glass Effects': 'Efectos de Cristal',
        'Neon Effects': 'Efectos Neón',
        'Clock Neon Effect': 'Efecto Neón del Reloj',
        'Clock Neon Color': 'Color Neón del Reloj',
        'Clock Neon Intensity': 'Intensidad Neón del Reloj',
        'Weather Neon Effect': 'Efecto Neón del Clima',
        'Weather Neon Color': 'Color Neón del Clima',
        'Container Neon Effect': 'Efecto Neón de Contenedores',
        'Container Neon Color': 'Color Neón de Contenedores',
        'Weather Information': 'Información del Clima',
        'Show Wind Speed': 'Mostrar Velocidad del Viento',
        'Show Humidity': 'Mostrar Humedad',
        'Show Pressure': 'Mostrar Presión',
        'Show Visibility': 'Mostrar Visibilidad',
        'Show Rain Chance': 'Mostrar Probabilidad de Lluvia',
        'Wind': 'Viento',
        'Humidity': 'Humedad',
        'Pressure': 'Presión',
        'Visibility': 'Visibilidad',
        'Rain Chance': 'Prob. Lluvia'
      }
    };

    // Weather icon mapping with SVG files
    const weatherIcons = {
      // Day icons
      'clear-day': 'src/icons/2682848_sunny_weather_forecast_day_sun.svg',
      'sunny': 'src/icons/2682848_sunny_weather_forecast_day_sun.svg',
      'fair': 'src/icons/2682849_sun_forecast_cloud_day_weather_cloudy.svg',
      'partly-cloudy': 'src/icons/2682849_sun_forecast_cloud_day_weather_cloudy.svg',
      'mostly-cloudy': 'src/icons/2682850_weather_clouds_cloud_cloudy_forecast.svg',
      'cloudy': 'src/icons/2682850_weather_clouds_cloud_cloudy_forecast.svg',
      'overcast': 'src/icons/2682850_weather_clouds_cloud_cloudy_forecast.svg',
      'rain': 'src/icons/2682834_day_weather_forecast_rain_cloud_rainy_sun.svg',
      'showers': 'src/icons/2682837_weather_forecast_rain_drop_sun_day_cloud.svg',
      'thunderstorms': 'src/icons/2682827_sun_cloud_day_light bolt_weather_rain_thunderstorm.svg',
      'snow': 'src/icons/2682815_precipitation_sun_snow_forecast_weather_day_cloud.svg',
      'fog': 'src/icons/2682812_weather_fog_mist_day_cloud_sun_coudy.svg',
      'haze': 'src/icons/2682812_weather_fog_mist_day_cloud_sun_coudy.svg',
      'windy': 'src/icons/2682832_sun_weather_forecast_windy_cloud_day_wind.svg',

      // Night icons
      'clear-night': 'src/icons/2682847_eclipse_forecast_moon_weather_night_space.svg',
      'partly-cloudy-night': 'src/icons/2682846_cloud_cloudy_forecast_weather_night_moon.svg',
      'mostly-cloudy-night': 'src/icons/2682846_cloud_cloudy_forecast_weather_night_moon.svg',
      'cloudy-night': 'src/icons/2682846_cloud_cloudy_forecast_weather_night_moon.svg',
      'rain-night': 'src/icons/2682833_weather_night_moon_precipitation_cloud_forecast_rain.svg',
      'snow-night': 'src/icons/2682814_snowing_snow_weather_night_precipitation_cloud_moon.svg',
      'fog-night': 'src/icons/2682811_fog_night_cloudy_mist_moon_weather_cloud.svg',
      'windy-night': 'src/icons/2682831_windy_forecast_weather_wind_night_moon_cloud.svg',
      'thunderstorms-night': 'src/icons/2682826_weather_rain_thunderstorm_light_night_bolt_moon.svg',

      // Default
      'default': 'src/icons/2682849_sun_forecast_cloud_day_weather_cloudy.svg'
    };

    // Initialize application
    function init() {
      loadSettings();
      setupEventListeners();
      initializeSettingsPanel();
      updateClock();
      updateLanguage();
      requestLocation();
      applySettings();

      // Update clock every second
      setInterval(updateClock, 1000);
    }

    // Load settings from localStorage
    function loadSettings() {
      const savedTheme = localStorage.getItem('clockTheme') || 'light';
      const savedLang = localStorage.getItem('clockLang') || 'es';
      const saved24Hour = localStorage.getItem('clock24Hour');
      const savedSettings = localStorage.getItem('clockSettings');

      state.theme = savedTheme;
      state.currentLang = savedLang;
      state.is24Hour = saved24Hour !== null ? JSON.parse(saved24Hour) : true;

      if (savedSettings) {
        state.settings = { ...state.settings, ...JSON.parse(savedSettings) };
      }

      applyTheme(savedTheme);
    }

    // Save settings to localStorage
    function saveSettings() {
      localStorage.setItem('clockTheme', state.theme);
      localStorage.setItem('clockLang', state.currentLang);
      localStorage.setItem('clock24Hour', JSON.stringify(state.is24Hour));
      localStorage.setItem('clockSettings', JSON.stringify(state.settings));
    }

    // Setup event listeners
    function setupEventListeners() {
      document.getElementById('settings-toggle').addEventListener('click', openSettings);
      document.getElementById('clock').addEventListener('click', toggleTimeFormat);

      // Settings panel listeners
      document.getElementById('settings-close').addEventListener('click', closeSettings);
      document.getElementById('settings-overlay').addEventListener('click', (e) => {
        if (e.target === e.currentTarget) closeSettings();
      });
      document.getElementById('save-settings').addEventListener('click', saveSettingsFromPanel);
      document.getElementById('reset-settings').addEventListener('click', resetSettings);

      // Theme and language in settings
      document.getElementById('theme-toggle-settings').addEventListener('click', toggleTheme);
      document.getElementById('lang-toggle-settings').addEventListener('click', toggleLanguage);
    }

    // Theme management
    function toggleTheme() {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
      applyTheme(state.theme);
      saveSettings();
    }

    function applyTheme(theme) {
      document.documentElement.setAttribute('data-theme', theme);
      const themeButton = document.getElementById('theme-toggle-settings');
      if (themeButton) {
        themeButton.textContent = theme === 'light' ? 'Oscuro' : 'Claro';
        themeButton.setAttribute('aria-label', `Switch to ${theme === 'light' ? 'dark' : 'light'} theme`);
      }

      // Update CSS variables based on theme
      const root = document.documentElement;
      if (theme === 'dark') {
        root.style.setProperty('--bg-color', '#000000');
        root.style.setProperty('--text-color', '#ffffff');
        root.style.setProperty('--web-background', '#000000');
        root.style.setProperty('--glass-color', 'rgba(0, 0, 0, 0.8)');
      } else {
        root.style.setProperty('--bg-color', state.settings.bgLightColor);
        root.style.setProperty('--text-color', state.settings.textLightColor);
        root.style.setProperty('--web-background', state.settings.webBackground);
        root.style.setProperty('--glass-color', `rgba(255, 255, 255, ${state.settings.glassOpacity})`);
      }
    }

    // Language management
    function toggleLanguage() {
      state.currentLang = state.currentLang === 'en' ? 'es' : 'en';
      const langButton = document.getElementById('lang-toggle-settings');
      if (langButton) {
        langButton.textContent = state.currentLang.toUpperCase();
      }
      document.documentElement.lang = state.currentLang;
      updateLanguage();
      updateClock(); // Refresh date display
      saveSettings();
    }

    function updateLanguage() {
      document.querySelectorAll('[data-en]').forEach(element => {
        const key = element.getAttribute(`data-${state.currentLang}`);
        if (key && translations[state.currentLang][key]) {
          element.textContent = translations[state.currentLang][key];
        }
      });
    }

    function translate(key) {
      return translations[state.currentLang][key] || key;
    }

    // Settings Panel Management
    function openSettings() {
      document.getElementById('settings-overlay').classList.add('active');
      updateSettingsPanel();
    }

    function closeSettings() {
      document.getElementById('settings-overlay').classList.remove('active');
    }

    function initializeSettingsPanel() {
      // Color pickers
      const colorPickers = [
        { id: 'web-background', prop: 'webBackground' },
        { id: 'text-light-color', prop: 'textLightColor' },
        { id: 'bg-light-color', prop: 'bgLightColor' },
        { id: 'text-dark-color', prop: 'textDarkColor' },
        { id: 'bg-dark-color', prop: 'bgDarkColor' },
        { id: 'clock-neon-color', prop: 'clockNeonColor' },
        { id: 'weather-neon-color', prop: 'weatherNeonColor' },
        { id: 'container-neon-color', prop: 'containerNeonColor' }
      ];

      colorPickers.forEach(picker => {
        const element = document.getElementById(picker.id);
        if (element) {
          element.addEventListener('input', (e) => {
            state.settings[picker.prop] = e.target.value;
            applySettings();
          });
        }
      });

      // Sliders
      const sliders = [
        { id: 'glass-opacity', prop: 'glassOpacity', suffix: '%', multiplier: 0.01 },
        { id: 'border-radius', prop: 'borderRadius', suffix: 'px' },
        { id: 'blur-intensity', prop: 'blurIntensity', suffix: 'px' },
        { id: 'clock-size', prop: 'clockSize', suffix: 'rem' },
        { id: 'date-size', prop: 'dateSize', suffix: 'rem' },
        { id: 'weather-size', prop: 'weatherSize', suffix: 'rem' },
        { id: 'clock-neon-intensity', prop: 'clockNeonIntensity', suffix: 'px' }
      ];

      sliders.forEach(slider => {
        const element = document.getElementById(slider.id);
        const valueDisplay = document.getElementById(slider.id + '-value');

        if (element && valueDisplay) {
          element.addEventListener('input', (e) => {
            let value = parseFloat(e.target.value);
            if (slider.multiplier) value *= slider.multiplier;
            state.settings[slider.prop] = value;
            valueDisplay.textContent = e.target.value + slider.suffix;
            applySettings();
          });
        }
      });

      // Toggle switches
      const toggles = [
        { id: 'clock-container-toggle', prop: 'showClockContainer' },
        { id: 'weather-container-toggle', prop: 'showWeatherContainer' },
        { id: 'date-toggle', prop: 'showDate' },
        { id: 'weather-location-toggle', prop: 'showWeatherLocation' },
        { id: 'clock-neon-toggle', prop: 'clockNeonEnabled' },
        { id: 'weather-neon-toggle', prop: 'weatherNeonEnabled' },
        { id: 'container-neon-toggle', prop: 'containerNeonEnabled' },
        { id: 'weather-wind-toggle', prop: 'showWeatherWind' },
        { id: 'weather-humidity-toggle', prop: 'showWeatherHumidity' },
        { id: 'weather-pressure-toggle', prop: 'showWeatherPressure' },
        { id: 'weather-visibility-toggle', prop: 'showWeatherVisibility' },
        { id: 'weather-rain-chance-toggle', prop: 'showWeatherRainChance' }
      ];

      toggles.forEach(toggle => {
        const element = document.getElementById(toggle.id);
        if (element) {
          element.addEventListener('click', () => {
            state.settings[toggle.prop] = !state.settings[toggle.prop];
            element.classList.toggle('active', state.settings[toggle.prop]);
            applySettings();
          });
        }
      });

      // Position selects
      document.getElementById('vertical-position').addEventListener('change', (e) => {
        state.settings.verticalPosition = e.target.value;
        applySettings();
      });

      document.getElementById('horizontal-position').addEventListener('change', (e) => {
        state.settings.horizontalPosition = e.target.value;
        applySettings();
      });
    }

    function updateSettingsPanel() {
      // Update color pickers safely
      const colorPickers = [
        { id: 'web-background', prop: 'webBackground' },
        { id: 'text-light-color', prop: 'textLightColor' },
        { id: 'bg-light-color', prop: 'bgLightColor' },
        { id: 'text-dark-color', prop: 'textDarkColor' },
        { id: 'bg-dark-color', prop: 'bgDarkColor' },
        { id: 'clock-neon-color', prop: 'clockNeonColor' },
        { id: 'weather-neon-color', prop: 'weatherNeonColor' },
        { id: 'container-neon-color', prop: 'containerNeonColor' }
      ];

      colorPickers.forEach(picker => {
        const element = document.getElementById(picker.id);
        if (element && state.settings[picker.prop]) {
          element.value = state.settings[picker.prop];
        }
      });

      // Update sliders and their values
      const sliders = [
        { id: 'glass-opacity', prop: 'glassOpacity', suffix: '%', multiplier: 100 },
        { id: 'border-radius', prop: 'borderRadius', suffix: 'px' },
        { id: 'blur-intensity', prop: 'blurIntensity', suffix: 'px' },
        { id: 'clock-size', prop: 'clockSize', suffix: 'rem' },
        { id: 'date-size', prop: 'dateSize', suffix: 'rem' },
        { id: 'weather-size', prop: 'weatherSize', suffix: 'rem' },
        { id: 'clock-neon-intensity', prop: 'clockNeonIntensity', suffix: 'px' }
      ];

      sliders.forEach(slider => {
        const element = document.getElementById(slider.id);
        const valueDisplay = document.getElementById(slider.id + '-value');
        if (element && valueDisplay && state.settings[slider.prop] !== undefined) {
          let value = state.settings[slider.prop];
          let displayValue = value;
          if (slider.multiplier) {
            displayValue = value * slider.multiplier;
            element.value = displayValue;
          } else {
            element.value = value;
          }
          valueDisplay.textContent = displayValue + slider.suffix;
        }
      });

      // Update toggles
      const toggles = [
        { id: 'clock-container-toggle', prop: 'showClockContainer' },
        { id: 'weather-container-toggle', prop: 'showWeatherContainer' },
        { id: 'date-toggle', prop: 'showDate' },
        { id: 'weather-location-toggle', prop: 'showWeatherLocation' },
        { id: 'clock-neon-toggle', prop: 'clockNeonEnabled' },
        { id: 'weather-neon-toggle', prop: 'weatherNeonEnabled' },
        { id: 'container-neon-toggle', prop: 'containerNeonEnabled' },
        { id: 'weather-wind-toggle', prop: 'showWeatherWind' },
        { id: 'weather-humidity-toggle', prop: 'showWeatherHumidity' },
        { id: 'weather-pressure-toggle', prop: 'showWeatherPressure' },
        { id: 'weather-visibility-toggle', prop: 'showWeatherVisibility' },
        { id: 'weather-rain-chance-toggle', prop: 'showWeatherRainChance' }
      ];

      toggles.forEach(toggle => {
        const element = document.getElementById(toggle.id);
        if (element) {
          element.classList.toggle('active', state.settings[toggle.prop]);
        }
      });

      // Update position selects
      const verticalSelect = document.getElementById('vertical-position');
      const horizontalSelect = document.getElementById('horizontal-position');
      if (verticalSelect) verticalSelect.value = state.settings.verticalPosition;
      if (horizontalSelect) horizontalSelect.value = state.settings.horizontalPosition;
    }

    function applySettings() {
      const root = document.documentElement;

      // Apply color settings
      root.style.setProperty('--web-background', state.settings.webBackground);
      root.style.setProperty('--bg-light-custom', state.settings.bgLightColor);
      root.style.setProperty('--text-light-custom', state.settings.textLightColor);
      root.style.setProperty('--bg-dark-custom', state.settings.bgDarkColor);
      root.style.setProperty('--text-dark-custom', state.settings.textDarkColor);

      // Apply current theme colors
      if (state.theme === 'dark') {
        root.style.setProperty('--bg-color', state.settings.bgDarkColor);
        root.style.setProperty('--text-color', state.settings.textDarkColor);
      } else {
        root.style.setProperty('--bg-color', state.settings.bgLightColor);
        root.style.setProperty('--text-color', state.settings.textLightColor);
      }

      // Apply glass effects
      root.style.setProperty('--glass-opacity', state.settings.glassOpacity);
      root.style.setProperty('--border-radius', state.settings.borderRadius + 'px');
      root.style.setProperty('--blur-intensity', state.settings.blurIntensity + 'px');

      // Apply font sizes
      root.style.setProperty('--clock-font-size', state.settings.clockSize + 'rem');
      root.style.setProperty('--date-font-size', state.settings.dateSize + 'rem');
      root.style.setProperty('--weather-font-size', state.settings.weatherSize + 'rem');

      // Apply neon effects with simple CSS
      const clockElement = document.getElementById('clock');
      const weatherTemp = document.querySelector('.weather-temp');

      if (clockElement) {
        if (state.settings.clockNeonEnabled) {
          clockElement.style.color = state.settings.clockNeonColor;
          clockElement.style.textShadow = `0 0 ${state.settings.clockNeonIntensity}px ${state.settings.clockNeonColor}`;
        } else {
          clockElement.style.color = 'var(--text-color)';
          clockElement.style.textShadow = 'none';
        }
      }

      if (weatherTemp) {
        if (state.settings.weatherNeonEnabled) {
          weatherTemp.style.color = state.settings.weatherNeonColor;
          weatherTemp.style.textShadow = `0 0 ${state.settings.weatherNeonIntensity}px ${state.settings.weatherNeonColor}`;
        } else {
          weatherTemp.style.color = 'var(--text-color)';
          weatherTemp.style.textShadow = 'none';
        }
      }

      // Apply visibility settings
      root.style.setProperty('--clock-container-display', state.settings.showClockContainer ? 'block' : 'none');
      root.style.setProperty('--weather-container-display', state.settings.showWeatherContainer ? 'flex' : 'none');
      root.style.setProperty('--date-display', state.settings.showDate ? 'block' : 'none');
      root.style.setProperty('--weather-location-display', state.settings.showWeatherLocation ? 'block' : 'none');

      // Apply weather details visibility
      root.style.setProperty('--weather-wind-display', state.settings.showWeatherWind ? 'block' : 'none');
      root.style.setProperty('--weather-humidity-display', state.settings.showWeatherHumidity ? 'block' : 'none');
      root.style.setProperty('--weather-pressure-display', state.settings.showWeatherPressure ? 'block' : 'none');
      root.style.setProperty('--weather-visibility-display', state.settings.showWeatherVisibility ? 'block' : 'none');
      root.style.setProperty('--weather-rain-chance-display', state.settings.showWeatherRainChance ? 'block' : 'none');

      // Apply positioning
      root.style.setProperty('--main-justify', state.settings.verticalPosition);
      root.style.setProperty('--main-align', state.settings.horizontalPosition);
    }

    function saveSettingsFromPanel() {
      saveSettings();
      closeSettings();
      // Show a brief confirmation (optional)
      console.log('Settings saved!');
    }

    function resetSettings() {
      // Reset to default values
      state.settings = {
        // Colors
        webBackground: '#ffffff',
        textLightColor: '#1d1d1f',
        bgLightColor: '#ffffff',
        textDarkColor: '#f5f5f7',
        bgDarkColor: '#000000',

        // Glass effects
        glassOpacity: 0,
        borderRadius: 24,
        blurIntensity: 20,

        // Font sizes
        clockSize: 6,
        dateSize: 1.5,
        weatherSize: 2.5,

        // Neon effects
        clockNeonEnabled: false,
        clockNeonColor: '#00d4ff',
        clockNeonIntensity: 20,
        clockNeonOpacity: 1,

        weatherNeonEnabled: false,
        weatherNeonColor: '#00d4ff',
        weatherNeonIntensity: 15,
        weatherNeonOpacity: 1,

        containerNeonEnabled: false,
        containerNeonColor: '#00d4ff',
        containerNeonIntensity: 10,
        containerNeonOpacity: 0.5,

        // Layout
        showClockContainer: true,
        showWeatherContainer: true,
        showDate: true,
        showWeatherLocation: true,
        verticalPosition: 'center',
        horizontalPosition: 'center',

        // Weather details
        showWeatherWind: false,
        showWeatherHumidity: false,
        showWeatherPressure: false,
        showWeatherVisibility: false,
        showWeatherRainChance: false
      };

      applySettings();
      updateSettingsPanel();
      saveSettings();
    }
    // Clock functionality
    function updateClock() {
      const now = new Date();
      let hours = now.getHours();
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      let period = '';
      if (!state.is24Hour) {
        period = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12;
      }
      hours = String(hours).padStart(2, '0');

      const timeString = `${hours}:${minutes}:${seconds}${!state.is24Hour ? ' ' + period : ''}`;
      document.getElementById('clock').textContent = timeString;

      // Update date
      updateDate(now);
    }

    function updateDate(date) {
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December'];

      const dayName = translate(days[date.getDay()]);
      const monthName = translate(months[date.getMonth()]);
      const day = date.getDate();
      const year = date.getFullYear();

      const dateString = `${dayName}, ${monthName} ${day}, ${year}`;
      document.getElementById('date').textContent = dateString;
    }

    function toggleTimeFormat() {
      state.is24Hour = !state.is24Hour;
      updateClock();
      saveSettings();
    }

    // Geolocation and Weather
    function requestLocation() {
      if (!navigator.geolocation) {
        showWeatherError(translate('Weather unavailable'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        position => {
          state.location = {
            lat: position.coords.latitude,
            lon: position.coords.longitude
          };
          fetchWeather();
        },
        error => {
          console.error('Geolocation error:', error);
          showWeatherError(translate('Location access denied'));
        },
        {
          timeout: 10000,
          enableHighAccuracy: false
        }
      );
    }

    async function fetchWeather() {
      if (!state.location) return;

      try {
        // First, get the weather station/grid point from coordinates
        const pointResponse = await fetch(
          `https://api.weather.gov/points/${state.location.lat},${state.location.lon}`
        );

        if (!pointResponse.ok) {
          throw new Error('Failed to get weather point data');
        }

        const pointData = await pointResponse.json();

        // Get current weather from the forecast office
        const forecastResponse = await fetch(pointData.properties.forecast);

        if (!forecastResponse.ok) {
          throw new Error('Failed to get weather forecast');
        }

        const forecastData = await forecastResponse.json();
        const currentPeriod = forecastData.properties.periods[0];

        // Get current conditions (this might not always be available)
        let currentTemp = null;
        try {
          const stationsResponse = await fetch(pointData.properties.observationStations);
          const stationsData = await stationsResponse.json();

          if (stationsData.features && stationsData.features.length > 0) {
            const stationUrl = stationsData.features[0].id;
            const observationResponse = await fetch(`${stationUrl}/observations/latest`);
            const observationData = await observationResponse.json();

            if (observationData.properties && observationData.properties.temperature.value) {
              // Convert Celsius to Fahrenheit
              const tempC = observationData.properties.temperature.value;
              currentTemp = Math.round((tempC * 9/5) + 32);
            }
          }
        } catch (obsError) {
          console.log('Could not get current observations, using forecast data');
        }

        // Try to get additional weather data from current observations
        let additionalData = {};
        try {
          const stationsResponse = await fetch(pointData.properties.observationStations);
          const stationsData = await stationsResponse.json();

          if (stationsData.features && stationsData.features.length > 0) {
            const stationUrl = stationsData.features[0].id;
            const observationResponse = await fetch(`${stationUrl}/observations/latest`);
            const observationData = await observationResponse.json();

            if (observationData.properties) {
              const props = observationData.properties;

              if (props.windSpeed?.value) {
                additionalData.windSpeed = `${Math.round(props.windSpeed.value * 2.237)} mph`;
              }
              if (props.relativeHumidity?.value) {
                additionalData.humidity = `${Math.round(props.relativeHumidity.value)}%`;
              }
              if (props.barometricPressure?.value) {
                additionalData.pressure = `${Math.round(props.barometricPressure.value / 100)} mb`;
              }
              if (props.visibility?.value) {
                additionalData.visibility = `${Math.round(props.visibility.value / 1609)} mi`;
              }
            }
          }
        } catch (obsError) {
          console.log('Could not get additional weather data');
        }

        state.weatherData = {
          temperature: currentTemp || currentPeriod.temperature,
          condition: currentPeriod.shortForecast,
          location: pointData.properties.relativeLocation?.properties?.city || 'Ubicación Desconocida',
          windSpeed: additionalData.windSpeed || null,
          humidity: additionalData.humidity || null,
          pressure: additionalData.pressure || null,
          visibility: additionalData.visibility || null,
          rainChance: currentPeriod.probabilityOfPrecipitation?.value ?
                     `${currentPeriod.probabilityOfPrecipitation.value}%` : null
        };

        displayWeather();

      } catch (error) {
        console.error('Weather fetch error:', error);
        showWeatherError(translate('Weather service error'));
      }
    }

    function displayWeather() {
      if (!state.weatherData) return;

      const weatherIcon = getWeatherIcon(state.weatherData.condition);
      const weatherContent = document.getElementById('weather-content');

      // Build additional weather details based on settings
      let detailsHTML = '';
      const details = [];

      if (state.settings.showWeatherWind && state.weatherData.windSpeed) {
        details.push(`
          <div class="weather-detail-item weather-wind">
            <img src="src/icons/2682842_weather_wind_speed_fast_breeze_windy.svg" alt="Wind" class="weather-detail-icon">
            <span class="weather-detail-value">${state.weatherData.windSpeed}</span>
          </div>
        `);
      }

      if (state.settings.showWeatherHumidity && state.weatherData.humidity) {
        details.push(`
          <div class="weather-detail-item weather-humidity">
            <img src="src/icons/2682807_rain_high_weather_percentage_precipitation_drop_humidity.svg" alt="Humidity" class="weather-detail-icon">
            <span class="weather-detail-value">${state.weatherData.humidity}</span>
          </div>
        `);
      }

      if (state.settings.showWeatherPressure && state.weatherData.pressure) {
        details.push(`
          <div class="weather-detail-item weather-pressure">
            <img src="src/icons/2682829_farenheit_forecast_degrees_weather_temprerature.svg" alt="Pressure" class="weather-detail-icon">
            <span class="weather-detail-value">${state.weatherData.pressure}</span>
          </div>
        `);
      }

      if (state.settings.showWeatherVisibility && state.weatherData.visibility) {
        details.push(`
          <div class="weather-detail-item weather-visibility">
            <img src="src/icons/2682821_weather_fog_forecast_mist_foggy.svg" alt="Visibility" class="weather-detail-icon">
            <span class="weather-detail-value">${state.weatherData.visibility}</span>
          </div>
        `);
      }

      if (state.settings.showWeatherRainChance && state.weatherData.rainChance) {
        details.push(`
          <div class="weather-detail-item weather-rain-chance">
            <img src="src/icons/2682839_weather_forecast_rain_drop_precipitation_humidity.svg" alt="Rain Chance" class="weather-detail-icon">
            <span class="weather-detail-value">${state.weatherData.rainChance}</span>
          </div>
        `);
      }

      if (details.length > 0) {
        detailsHTML = `<div class="weather-details-grid">${details.join('')}</div>`;
      }

      weatherContent.innerHTML = `
        <div class="weather-location">${state.weatherData.location}</div>
        <div class="weather-main-info">
          <div class="weather-icon">${weatherIcon}</div>
          <div class="weather-temp">${state.weatherData.temperature}°F</div>
        </div>
        ${detailsHTML}
      `;
    }

    function getWeatherIcon(condition) {
      const now = new Date();
      const hour = now.getHours();
      const isNight = hour < 6 || hour > 20; // Simple day/night detection

      const conditionLower = condition.toLowerCase();

      // Check for specific conditions with day/night variants
      if (conditionLower.includes('clear') || conditionLower.includes('sunny')) {
        return createSVGIcon(isNight ? weatherIcons['clear-night'] : weatherIcons['clear-day']);
      }
      if (conditionLower.includes('partly cloudy') || conditionLower.includes('partly sunny')) {
        return createSVGIcon(isNight ? weatherIcons['partly-cloudy-night'] : weatherIcons['partly-cloudy']);
      }
      if (conditionLower.includes('mostly cloudy')) {
        return createSVGIcon(isNight ? weatherIcons['mostly-cloudy-night'] : weatherIcons['mostly-cloudy']);
      }
      if (conditionLower.includes('cloudy') || conditionLower.includes('overcast')) {
        return createSVGIcon(isNight ? weatherIcons['cloudy-night'] : weatherIcons['cloudy']);
      }
      if (conditionLower.includes('rain') || conditionLower.includes('drizzle')) {
        return createSVGIcon(isNight ? weatherIcons['rain-night'] : weatherIcons['rain']);
      }
      if (conditionLower.includes('shower')) {
        return createSVGIcon(weatherIcons['showers']);
      }
      if (conditionLower.includes('thunder') || conditionLower.includes('storm')) {
        return createSVGIcon(isNight ? weatherIcons['thunderstorms-night'] : weatherIcons['thunderstorms']);
      }
      if (conditionLower.includes('snow') || conditionLower.includes('blizzard')) {
        return createSVGIcon(isNight ? weatherIcons['snow-night'] : weatherIcons['snow']);
      }
      if (conditionLower.includes('fog') || conditionLower.includes('mist') || conditionLower.includes('haze')) {
        return createSVGIcon(isNight ? weatherIcons['fog-night'] : weatherIcons['fog']);
      }
      if (conditionLower.includes('wind')) {
        return createSVGIcon(isNight ? weatherIcons['windy-night'] : weatherIcons['windy']);
      }

      // Default icon
      return createSVGIcon(weatherIcons['default']);
    }

    function createSVGIcon(iconPath) {
      return `<img src="${iconPath}" alt="Weather Icon" class="weather-svg-icon">`;
    }

    function showWeatherError(message) {
      const weatherContent = document.getElementById('weather-content');
      weatherContent.innerHTML = `<div class="error">${message}</div>`;
    }

    // Initialize the application when DOM is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  </script>
</body>
</html>